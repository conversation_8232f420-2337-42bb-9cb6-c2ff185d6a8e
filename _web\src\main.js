import 'core-js/stable'
import 'regenerator-runtime/runtime'
import './core/lazy_use'
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store/'
import { VueAxios } from './utils/request'
import bootstrap from './core/bootstrap'
import './permission'
import './utils/filter'
import './components/global.less'
import { Dialog } from '@/components'
import { hasBtnPermission } from './utils/permissions'
import { sysApplication } from './utils/applocation'
import "vue-easytable/libs/theme-default/index.css";
import { VeTable,VeLoading } from "vue-easytable"
import 'vxe-table/lib/style.css'
import Vue2OrgTree from 'vue-tree-color'
Vue.use(Vue2OrgTree)
import '@/assets/fonts/font.css'

// 引入全局样式
import '@/style/index.css'
import './core/pbiComponents.js'
import 'animate.css'

import VueMathJax from 'vue-mathjax'
import VueCompositionAPI from '@vue/composition-api'

/* import FileViewer from "file-viewer";

Vue.use(FileViewer) */


Vue.use(VueAxios)
Vue.use(Dialog)
Vue.use(VeTable)
Vue.prototype.hasPerm = hasBtnPermission
Vue.prototype.applocation = sysApplication
Vue.config.productionTip = false
Vue.prototype.$veLoading = VeLoading

Vue.use(VueCompositionAPI)
Vue.use(VueMathJax)

import * as echarts from 'echarts'
Vue.prototype.echarts = echarts
Vue.directive('focus',{
  inserted: function (el) {
    // 聚焦元素
    el.focus()
  }
}

)

new Vue({
  router,
  store,
  created: bootstrap,
  render: h => h(App)
}).$mount('#app')
