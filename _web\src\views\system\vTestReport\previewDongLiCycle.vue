<template>
  <a-spin :spinning="isLoading" class="hide-progress-parent">
  <div ref="wrapper" class="wrapper">
    <div class="flex-sb-center-row">
      <div class="head_title">{{ "循环寿命: " + data.reportName }}</div>
    </div>
    <div class="right-top ant-btn-div">
      <a-button class="mr10" size="small" @click="exportCycleReport">
        <a-icon type="download"/>
        导出数据
      </a-button>
    </div>

    <div class="all-wrapper mt10">
      <div class="left-content block">
        <div v-for="item in editObjList">
          <div class="flex-sb-center-row mt2" v-if="allDataJson.hasCycStepParam !== 'no' && item.includes('cyc') || allDataJson.hasRptStepParam !== 'no' && item.includes('rpt')">
            <pageComponent :editObj="item" @down="handleDown(item)" @edit="handleEditEcharts(item)"></pageComponent>
            <div class="ant-btn-div">
              <a-tooltip title="编辑建模参数">
                <a-button size="small" @click="reExport(false)"><a-icon type="edit"/></a-button>
              </a-tooltip>
              <a-popconfirm title="确定要重新生成吗?" ok-text="确定" cancel-text="取消" @confirm="reExport(true)">
                <a-tooltip title="重新生成">
                  <a-button class="ml10" size="small"><a-icon type="sync"/></a-button>
                </a-tooltip>
              </a-popconfirm>
              <a-radio-group class="ml10" v-if="allDataJson && allDataJson.needEtp" size="small" :value="xAxisType" @change="xAxisTypeChange">
                <a-radio-button value="cycle">CycleNumber</a-radio-button>
                <a-radio-button value="etp">ETP</a-radio-button>
              </a-radio-group>
            </div>
          </div>
          <div :ref="item" :id="item" style="width: 595px; height: 415px; border: 0.5px solid #ccc;" v-show="allDataJson.hasCycStepParam !== 'no' && item.includes('cyc') || allDataJson.hasRptStepParam !== 'no' && item.includes('rpt')"></div>
        </div>
      </div>

      <div class="right-content block flex-column">
        <div class="table-title-div mt2" v-if="allDataJson.hasCycStepParam !== 'no'">Cycle容量数据</div>
        <div style="height: 415px;" v-if="allDataJson.hasCycStepParam !== 'no'">
          <a-table :data-source="cycCapTableList"
                   :columns="cycCapColumns"
                   :rowKey="record => record.cycle"
                   :pagination="false"
                   :loading="tableLoading || cycCapLoading"
                   bordered>
          </a-table>
          <a-pagination style="margin-top: 2px; float: right;"
                        :current="cycCapPageNo"
                        :pageSize="cycCapPageSize"
                        :total="cycCapTotal"
                        :showSizeChanger="true"
                        :pageSizeOptions="paginationConfig.pageSizeOptions"
                        size="small"
                        :showTotal="(total, range) => `${range[0]}-${range[1]} 共 ${total} 条`"
                        @change="onCycCapPageChange" @showSizeChange="onCycCapPageChange">
          </a-pagination>
        </div>

        <div class="table-title-div mt2" v-if="allDataJson.hasCycStepParam !== 'no'">Cycle能量数据</div>
        <div style="height: 415px;" v-if="allDataJson.hasCycStepParam !== 'no'">
          <a-table :data-source="cycEngTableList"
                   :columns="cycEngColumns"
                   :rowKey="record => record.cycle"
                   :pagination="false"
                   :loading="tableLoading || cycEngLoading"
                   bordered>
          </a-table>
          <a-pagination style="margin-top: 2px; float: right;"
                        :current="cycEngPageNo"
                        :pageSize="cycEngPageSize"
                        :total="cycEngTotal"
                        :showSizeChanger="true"
                        :pageSizeOptions="paginationConfig.pageSizeOptions"
                        size="small"
                        :showTotal="(total, range) => `${range[0]}-${range[1]} 共 ${total} 条`"
                        @change="onCycEngPageChange" @showSizeChange="onCycEngPageChange">
          </a-pagination>
        </div>

        <div class="table-title-div mt2" v-if="allDataJson.hasRptStepParam !== 'no'">RPT容量数据</div>
        <div style="height: 415px;" v-if="allDataJson.hasRptStepParam !== 'no'">
          <a-table :data-source="rptTableList"
                   :columns="rptCapColumns"
                   :rowKey="record => record.rptCycle"
                   :pagination="paginationConfig"
                   :loading="tableLoading"
                   bordered>
          </a-table>
        </div>

        <div class="table-title-div mt2" v-if="allDataJson.hasRptStepParam !== 'no'">RPT能量数据</div>
        <div style="height: 415px;" v-if="allDataJson.hasRptStepParam !== 'no'">
          <a-table :data-source="rptTableList"
                   :columns="rptEngColumns"
                   :rowKey="record => record.rptCycle"
                   :pagination="paginationConfig"
                   :loading="tableLoading"
                   bordered>
          </a-table>
        </div>
      </div>
    </div>

    <!-- 在线编辑图表 -->
    <div v-if="drawerVisible">
      <PreviewDrawer
        :screenImageId = "screenImageId"
        :templateParam = "reportChartTemplateList[editObj]"
        :legendNameTypeShow="true"
        :LegendNameTypeList = "chartLegendNameListObj[editObj]"
        :legendOptions="legendOptions[editObj]"
        :original="originalData[editObj]"
        :data="editData[editObj].series"
        :editData="editData[editObj]"
        :checkObj="chartCheckObj[editObj]"
        @submit="handleDrawerSubmit"
        @reset="handleDrawerReset"
        @close="() => this.drawerVisible = false"
        @changeTemplate ="handleChangeTemplate"
        @screenshot="handleScreenshot">
      </PreviewDrawer>
    </div>

    <div class="action-bar">
      <pbiReturnTop width="20" height="20" color="#333" @returnTop="handleReturnTop"></pbiReturnTop>
    </div>

  </div>
  </a-spin>
</template>
<script>
import {getDongLiCycleReport, exportDongLiCycleReport} from "@/api/modular/system/cycleReportManager";
import {optionMergeCommon} from "@/views/system/vTestReport/mixin/optionMergeCommon";
import {chartTemplate} from "@/views/system/vTestReport/mixin/chartTemplate";
import _ from "lodash";
import {Pagination} from "ant-design-vue";
import {downloadfile1} from "@/utils/util";
import jsonBigint from "json-bigint";

export default {
  components: {
    'a-pagination': Pagination
  },
  mixins: [optionMergeCommon,chartTemplate],
  data: function () {
    return {
      isLoading: false,
      id: null,
      data: {},
      allDataJson: {},
      xAxisType: 'cycle',
      queryParam: {},
      dcirTiTleList: [],
      hasChCeStep: false,
      hasDcirGroup: false,
      title: '',
      dchCeStepTiTleList: [],
      hasRptChCeStep: false,

      editObjList: ['cycCap', 'cycEng', 'rptCap', 'rptEng'],

      cycCapColumns: [
        {
          title: "循环号",
          dataIndex: "cycle",
          align: "center",
          width: 60,
        },
        {
          title: "放电容量/Ah",
          align: "center",
        },
        {
          title: "容量保持率/%",
          align: "center",
        },
      ],
      cycEngColumns: [
        {
          title: "循环号",
          dataIndex: "cycle",
          align: "center",
          width: 60,
        },
        {
          title: "放电能量/Wh",
          align: "center",
        },
        {
          title: "能量保持率/%",
          align: "center",
        }
      ],
      rptCapColumns: [
        {
          title: "循环圈数",
          dataIndex: "rptCycle",
          align: "center",
          width: 70,
        },
        {
          title: "放电容量/Ah",
          align: "center",
        },
        {
          title: "容量保持率/%",
          align: "center",
        },
      ],
      rptEngColumns: [
        {
          title: "循环圈数",
          dataIndex: "rptCycle",
          align: "center",
          width: 70,
        },
        {
          title: "放电能量/Wh",
          align: "center",
        },
        {
          title: "能量保持率/%",
          align: "center",
        },
      ],

      cycCapTablePage: {},
      cycEngTablePage: {},
      cycCapTableList: [],
      cycEngTableList: [],
      rptTableList: [],

      tableLoading: false,
      cycCapLoading: false,
      cycEngLoading: false,
      paginationConfig: {
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '30', '40', '50'], // 显示的每页数量选项
        size: "small",
        showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
      },
      cycCapPageNo: 1,
      cycCapPageSize: 10,
      cycCapTotal: 50,
      cycEngPageNo: 1,
      cycEngPageSize: 10,
      cycEngTotal: 50,

      cycCapEchartTitle: '',
      cycEngEchartTitle: '',
      rptCapEchartTitle: '',
      rptEngEchartTitle: '',

      echartsColorList: [
        "#c00000",
        "#0070c0",
        "#808080",
        "#7030a0",
        "#4472c4",
        "#a5a5a5",
        "#ed7d31",
        "#5b9bd5",
        "#70ad47",
        "#000000",
        "#ff9999",
        "#ffc000",
        "#00b050"
      ],

      drawerVisible: false,
      echartObj: {},
      editObj: null,
      chartCheckObj: {
        cycCap: {},
        cycEng: {},
        rptCap: {},
        rptEng: {},
      },

      firstInit: {
        cycCap: true,
        cycEng: true,
        rptCap: true,
        rptEng: true,
      },

      titleDataObj: {
        cycCap: {
          legendNameType: 'sampleCode',
        },
        cycEng: {
          legendNameType: 'sampleCode',
        },
        rptCap: {
          legendNameType: 'sampleCode',
        },
        rptEng: {
          legendNameType: 'sampleCode',
        },
      },
      chartLegendNameListObj: {
        cycCap: [],
        cycEng: [],
        rptCap: [],
        rptEng: [],
      }, //不同类型图例的汇总  合并
      originalData: {}, //原始数据
      editData: {}, //编辑数据
      checkObj: {}, //暂时无用属性

      legendOptions:{} //图例-数据的选择项
    };
  },
  async mounted() {
    // 获得该图表的历史修改
    await this.getChartTemplateRelationList(this.$route.query.id,this.editObjList)
    this.init()
  },

  methods: {
    init() {
      this.tableLoading = true

      this.id = this.$route.query.id
      getDongLiCycleReport({
        id: this.id,
        cycCapPageNo: this.cycCapPageNo, cycCapPageSize: this.cycCapPageSize,
        cycEngPageNo: this.cycEngPageNo, cycEngPageSize: this.cycEngPageSize
      })
        .then(res => {
          if (res.data) {
            this.data = res.data
            let json = jsonBigint({storeAsString: true})
            this.allDataJson = json.parse(res.data.allDataJson)
            this.queryParam = json.parse(res.data.queryParam)
            this.dcirTiTleList = Array.isArray(this.queryParam.dcirTiTleList) ? this.queryParam.dcirTiTleList.filter(item => typeof item === 'string' && item.length > 0) : []
            this.hasChCeStep = Array.isArray(this.queryParam.cycleStepParamList) && this.queryParam.cycleStepParamList.some(item => item.chCeStep || Array.isArray(item.chCeStepList) && item.chCeStepList.length > 0)
            this.hasDcirGroup = this.dcirTiTleList.length > 0

            this.dchCeStepTiTleList = Array.isArray(this.queryParam.dchCeStepTiTleList) ? this.queryParam.dchCeStepTiTleList : []
            this.hasRptChCeStep = Array.isArray(this.queryParam.rptStepParamList) &&  this.queryParam.rptStepParamList.some(item => item.chCeStep || Array.isArray(item.chCeStepList) && item.chCeStepList.length > 0)

            this.title = (this.queryParam.projectName ? this.queryParam.projectName + '_' : '') + (this.queryParam.phase ? this.queryParam.phase + '_' : '') + (this.queryParam.temp || this.queryParam.temp == 0 ? this.queryParam.temp + '℃_' : '')
              + (this.queryParam.rate ? this.queryParam.rate + (/^\d+(\.\d+)?$/.test(this.queryParam.rate) ? 'C' : '') + '_' : '') + (this.queryParam.startSoc || this.queryParam.startSoc == 0 ? this.queryParam.startSoc + '~' + this.queryParam.endSoc + '%SOC_' : '')
            this.cycCapEchartTitle = this.title + 'Cycle Life'
            this.cycEngEchartTitle = this.title + 'Cycle Life'
            this.rptCapEchartTitle = this.title + 'RPT of Cycle Life'
            this.rptEngEchartTitle = this.title + 'RPT of Cycle Life'

            // Cycle数据分页
            this.cycCapTablePage = this.allDataJson.cycCapTablePage || {}
            this.cycCapTableList = this.cycCapTablePage.records
            this.cycCapPageNo = this.cycCapTablePage.current
            this.cycCapPageSize = this.cycCapTablePage.size
            this.cycCapTotal = this.cycCapTablePage.total

            this.cycEngTablePage = this.allDataJson.cycEngTablePage || {}
            this.cycEngTableList = this.cycEngTablePage.records
            this.cycEngPageNo = this.cycEngTablePage.current
            this.cycEngPageSize = this.cycEngTablePage.size
            this.cycEngTotal = this.cycEngTablePage.total

            this.rptTableList = this.allDataJson.rptTableList
          }
        })
        .then(res => {
          // 右边表格数据
          this.initTable()
          this.tableLoading = false

          // Echarts图初始化
          this.editObjList.forEach(editObj => {
            this.initEchart(editObj, this.xAxisType, `${editObj}RetRateEchartList`, editObj.includes('rpt') ? 'rptDcirIncRateEchartList' : null)
          })

          // 字体渲染不正确，重新setOption
          document.fonts.ready.then(() => {
            this.editObjList.forEach(editObj => {
              this.echartObj[editObj].setOption({
                textStyle: {
                  fontFamily: "Times New Roman"
                }
              })
            })
          })

        })
    },

    initTable() {
      // 以sampleCodeList电芯为准，处理表头
      let sampleCodeList = this.allDataJson.sampleCodeList
      let needEtp = this.allDataJson.needEtp

      if (this.cycCapTableList.length > 0) {
        let primaryObjectMapOne = this.cycCapTableList[0].primaryObjectMap

        if (Array.isArray(sampleCodeList) && sampleCodeList.length > 0) {
          this.cycCapColumns[1].children = []
          this.cycCapColumns[2].children = []
          this.cycEngColumns[1].children = []
          this.cycEngColumns[2].children = []

          if (this.hasChCeStep) {
            this.cycCapColumns.push(
              {
                title: "充电容量/Ah",
                align: "center",
                children: []
              }
            )
            this.cycEngColumns.push(
              {
                title: "充电能量/Wh",
                align: "center",
                children: []
              }
            )
          }

          if (needEtp) {
            this.cycCapColumns.push(
              {
                title: "ETP/Wh",
                align: "center",
                children: []
              }
            )
            this.cycEngColumns.push(
              {
                title: "ETP/Wh",
                align: "center",
                children: []
              }
            )
          }

          for (let i = 0; i < sampleCodeList.length; i++) {
            let sampleCode = sampleCodeList[i]
            let batteryCode = primaryObjectMapOne[sampleCode].batteryCode || sampleCode

            this.cycCapColumns[1].children.push(
              this.getChildren(sampleCode, batteryCode, 'capacity')
            )
            this.cycCapColumns[2].children.push(
              this.getChildren(sampleCode, batteryCode, 'capRetRate')
            )

            this.cycEngColumns[1].children.push(
              this.getChildren(sampleCode, batteryCode, 'energy')
            )
            this.cycEngColumns[2].children.push(
              this.getChildren(sampleCode, batteryCode, 'engRetRate')
            )

            if (this.hasChCeStep) {
              this.cycCapColumns[3].children.push(
                this.getChildren(sampleCode, batteryCode, 'chCapacity')
              )
              this.cycEngColumns[3].children.push(
                this.getChildren(sampleCode, batteryCode, 'chEnergy')
              )
            }

            const colIndex = this.cycCapColumns.findIndex(item => item.title === 'ETP/Wh')
            if (needEtp && colIndex !== -1) {
              this.cycCapColumns[colIndex].children.push(
                this.getChildren(sampleCode, batteryCode, 'etp')
              )
              this.cycEngColumns[colIndex].children.push(
                this.getChildren(sampleCode, batteryCode, 'etp')
              )
            }
          }
        }
      }

      if (this.queryParam.ceStepTiTle) {
        this.rptCapColumns[1].title = this.queryParam.ceStepTiTle + '_' + '放电容量/Ah'
        this.rptEngColumns[1].title = this.queryParam.ceStepTiTle + '_' + '放电能量/Wh'
      }

      // 以sampleCodeList为准，处理表头
      if (this.rptTableList.length > 0) {
        let primaryObjectMapOne = this.rptTableList[0].primaryObjectMap

        if (Array.isArray(sampleCodeList) && sampleCodeList.length > 0) {
          this.rptCapColumns[1].children = []
          this.rptCapColumns[2].children = []
          for (let i = 0; i < this.dchCeStepTiTleList.length; i++) {
            this.rptCapColumns.push(
                {
                  title: this.dchCeStepTiTleList[i] + "_放电容量/Ah",
                  align: "center",
                  children: []
                }
            )
          }
          if (this.hasDcirGroup) {
            for (let i = 0; i < this.dcirTiTleList.length; i++) {
              this.rptCapColumns.push(
                {
                  title: this.dcirTiTleList[i] + "_DCIR/mΩ",
                  align: "center",
                  children: []
                },
                {
                  title: this.dcirTiTleList[i] + "_DCIR增长率/%",
                  align: "center",
                  children: []
                }
              )
            }
          }

          this.rptEngColumns[1].children = []
          this.rptEngColumns[2].children = []
          for (let i = 0; i < this.dchCeStepTiTleList.length; i++) {
            this.rptEngColumns.push(
                {
                  title: this.dchCeStepTiTleList[i] + "_放电能量/Wh",
                  align: "center",
                  children: []
                }
            )
          }
          if (this.hasDcirGroup) {
            for (let i = 0; i < this.dcirTiTleList.length; i++) {
              this.rptEngColumns.push(
                {
                  title: this.dcirTiTleList[i] + "_DCIR/mΩ",
                  align: "center",
                  children: []
                },
                {
                  title: this.dcirTiTleList[i] + "_DCIR增长率/%",
                  align: "center",
                  children: []
                }
              )
            }
          }

          if (this.hasRptChCeStep) {
            this.rptCapColumns.push(
                {
                  title: "充电容量/Ah",
                  align: "center",
                  children: []
                },
                {
                  title: "充电恒流比",
                  align: "center",
                  children: []
                }
            )
            this.rptEngColumns.push(
                {
                  title: "充电能量/Wh",
                  align: "center",
                  children: []
                }
            )
          }

          if (needEtp) {
            this.rptCapColumns.push(
              {
                title: "ETP/Wh",
                align: "center",
                children: []
              }
            )
            this.rptEngColumns.push(
              {
                title: "ETP/Wh",
                align: "center",
                children: []
              }
            )
          }

          for (let i = 0; i < sampleCodeList.length; i++) {
            let sampleCode = sampleCodeList[i]
            let batteryCode = primaryObjectMapOne[sampleCode].batteryCode || sampleCode

            this.rptCapColumns[1].children.push(
              this.getChildren(sampleCode, batteryCode, 'capacity')
            )
            this.rptCapColumns[2].children.push(
              this.getChildren(sampleCode, batteryCode, 'capRetRate')
            )
            for (let j = 0; j < this.dchCeStepTiTleList.length; j++) {
              this.rptCapColumns[3 + j].children.push(
                  this.getChildren(sampleCode, batteryCode, 'dchCapacity'+j)
              )
            }
            if (this.hasDcirGroup) {
              for (let j = 0; j < this.dcirTiTleList.length; j++) {
                this.rptCapColumns[this.dchCeStepTiTleList.length + 3 + 2 * j].children.push(
                  this.getChildren(sampleCode, batteryCode, 'dcir'+j)
                )
                this.rptCapColumns[this.dchCeStepTiTleList.length + 4 + 2 * j].children.push(
                  this.getChildren(sampleCode, batteryCode, 'dcirIncRate'+j)
                )
              }
            }

            this.rptEngColumns[1].children.push(
              this.getChildren(sampleCode, batteryCode, 'energy')
            )
            this.rptEngColumns[2].children.push(
              this.getChildren(sampleCode, batteryCode, 'engRetRate')
            )
            for (let j = 0; j < this.dchCeStepTiTleList.length; j++) {
              this.rptEngColumns[3 + j].children.push(
                  this.getChildren(sampleCode, batteryCode, 'dchEnergy'+j)
              )
            }
            if (this.hasDcirGroup) {
              for (let j = 0; j < this.dcirTiTleList.length; j++) {
                this.rptEngColumns[this.dchCeStepTiTleList.length + 3 + 2*j].children.push(
                  this.getChildren(sampleCode, batteryCode, 'dcir'+j)
                )
                this.rptEngColumns[this.dchCeStepTiTleList.length + 4 + 2*j].children.push(
                  this.getChildren(sampleCode, batteryCode, 'dcirIncRate'+j)
                )
              }
            }

            if (this.hasRptChCeStep) {
              this.rptCapColumns[3 + this.dchCeStepTiTleList.length + 2 * this.dcirTiTleList.length].children.push(
                  this.getChildren(sampleCode, batteryCode, 'chCapacity')
              )
              this.rptCapColumns[4 + this.dchCeStepTiTleList.length + 2 * this.dcirTiTleList.length].children.push(
                  this.getChildren(sampleCode, batteryCode, 'chCccapacityRate')
              )
              this.rptEngColumns[3 + this.dchCeStepTiTleList.length + 2 * this.dcirTiTleList.length].children.push(
                  this.getChildren(sampleCode, batteryCode, 'chEnergy')
              )
            }
            if (needEtp) {
              const index1 = 3 + this.dchCeStepTiTleList.length + 2 * this.dcirTiTleList.length + (this.hasRptChCeStep ? 2 : 0)
              const index2 = 3 + this.dchCeStepTiTleList.length + 2 * this.dcirTiTleList.length + (this.hasRptChCeStep ? 1 : 0)
              this.rptCapColumns[index1].children.push(
                this.getChildren(sampleCode, batteryCode, 'etp')
              )
              this.rptEngColumns[index2].children.push(
                this.getChildren(sampleCode, batteryCode, 'etp')
              )
            }
          }
        }
      }

    },
    getChildren(sampleCode, batteryCode, columnKey) {
      let result = {
        title: batteryCode,
        align: "center",
        width: "100px",
        dataIndex: `primaryObjectMap.${sampleCode}.${columnKey}`
        /*
        customRender: (text, record) => {
          // return <a-tooltip title={ record.primaryObjectMap[sampleCode].cycleId }>{ record.primaryObjectMap[sampleCode][columnKey] }</a-tooltip>
          return record.primaryObjectMap[sampleCode][columnKey]
        }
        */
      }

      if (sampleCode !== batteryCode) {
        result = {
          title: sampleCode,
          align: "center",
          width: "100px",
          children: [
            result
          ]
        }
      }

      return result
    },

    initEchart(targetObj, xAxisType = 'cycle', yAxisOneListName = null, yAxisTwoListName = null) {
      // 检查是否存在 ECharts 实例 并清除
      if (this.echartObj[targetObj]) {
        this.echartObj[targetObj].dispose();
      }

      // this.echartObj[targetObj] = this.echarts.init(document.getElementById(targetObj), 'walden', { renderer: "svg" }) // 测试
      this.echartObj[targetObj] = this.echarts.init(document.getElementById(targetObj), 'walden', {devicePixelRatio: 2})

      // 模板数据
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson
      const originalParam = this.reportChartTemplateList[targetObj].originalParamJson

      let seriesList = []

      if (this.firstInit[targetObj]) {  // 首次加载
        let yAxisOneList = _.cloneDeep(this.allDataJson[yAxisOneListName])
        let yAxisTwoList = yAxisTwoListName !== null ? _.cloneDeep(this.allDataJson[yAxisTwoListName]) : []
        seriesList = this._handleCycleEchartData(targetObj, xAxisType, yAxisOneList, yAxisTwoList)
      } else {  // 二次加载
        seriesList = this.editData[targetObj].editSeries
      }

      let chartOption = this._handleCycleEchartOptions(targetObj, xAxisType, seriesList)

      // 设置X轴最大值，有CYCLE层数据以CYCLE为准；无CYCLE层数据以工程师填写圈数为准
      if ((this.firstInit[targetObj] || this.editData[targetObj].targetResetObj === 'xMax') && this.globalXMax !== 0 && !templateParam.xMax) {
        chartOption.xAxis[0].max = this.globalXMax
        chartOption.xAxis[0].interval = this.globalXInterval
      }

      // 如果模板有X轴的最大值、最小值、间隔,如果有就设置
      if(templateParam.xMin){
        chartOption.xAxis[0].min = templateParam.xMin
      }
      if(templateParam.xMax){
        chartOption.xAxis[0].max = templateParam.xMax
      }
      if(templateParam.xInterval){
        chartOption.xAxis[0].interval = templateParam.xInterval
      }


      this.echartObj[targetObj].clear()
      this.echartObj[targetObj].getZr().off('dblclick')
      this.echartObj[targetObj].getZr().on('dblclick', ({target, topTarget}) => {
        this._handleDblclickEchart(target, topTarget, targetObj)
      });
      this.echartObj[targetObj].setOption(chartOption)


      if (this.firstInit[targetObj]) {
        this._handleYAxisValue(targetObj, targetObj.includes('rpt'))

        // 为图例多一个格子：setOption后才能拿到X轴最大值间隔值，设置图表的 globalXMax、globalXInterval
        if (this.globalXMax === 0 && !templateParam.xMax && this.editData[targetObj].xMax > 1) {
          this.editData[targetObj].xMax = this.editData[targetObj].xMax + this.editData[targetObj].xInterval
          this.globalXMax = this.editData[targetObj].xMax
          this.globalXInterval = this.editData[targetObj].xInterval
          this.originalData[targetObj].xMax = this.editData[targetObj].xMax
          this.originalData[targetObj].xInterval = this.editData[targetObj].xInterval

          // 重新设置X轴
          chartOption.xAxis[0].max = this.globalXMax
          chartOption.xAxis[0].interval = this.globalXInterval
          this.echartObj[targetObj].setOption(chartOption)
        }

        this.firstInit[targetObj] = false
      }
    },

    // 数据处理(处理初始值)
    _handleCycleEchartData(targetObj, xAxisType = 'cycle', yAxisOneList, yAxisTwoList = []) {
      // 模板数据
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson
      const originalParam = this.reportChartTemplateList[targetObj].originalParamJson

      yAxisOneList = Array.isArray(yAxisOneList) ? yAxisOneList : []

      // 基础数据
      let firstTooltip = {title: targetObj.includes('Cap') ? '容量保持率  ' : '能量保持率  ', unit: ' %'}
      let titleData = {
        chartTitle: templateParam.chartTitle ?? this[`${targetObj}EchartTitle`],
        XTitle: templateParam.XTitle ?? (xAxisType === 'cycle' ? 'Cycle Number / N' : 'ETP / Wh'),
        YTitle: templateParam.YTitle ?? (targetObj.includes('Cap') ? 'Capacity Retention Rate / %' : 'Energy Retention Rate / %'),
        legendNameType: templateParam.legendNameType ?? (this.titleDataObj[targetObj].legendNameType || 'sampleCode'),
        legendRight:templateParam.legendRight ?? (targetObj.includes('cyc') ? 45 : 80),
      }
      if (targetObj.includes('rpt')) {
        titleData.yType2 = templateParam.YTitle2 ?? 'value'
        titleData.YTitle2 = templateParam.YTitle2 ?? 'DCIR Increase Rate / %'
      }

      titleData = this._getEchartOriginal(titleData, targetObj, targetObj.includes('rpt'))


      // 每根线的数据
      let seriesList = []
      let lineColorList = [] // 折线颜色
      const echartsColorList = this.echartsColorList

      // 定义通用值
      const normalData = {...titleData,series:[]}
      const normalLegent = []

      for (let i = 0; i < yAxisOneList.length; i++) {
        const sampleCode = yAxisOneList[i].sampleCode;
        const yAxisOneLegendName = yAxisOneList[i][titleData.legendNameType]
        let templateContent = (templateParam.checkData && templateParam.checkData.length !== 0) ? (templateParam.checkData.filter(item => item.id === sampleCode)[0] || {}) : {}

        this.chartLegendNameListObj[targetObj].push({sampleCode: sampleCode, batteryCode: yAxisOneList[i].batteryCode})
        const haveColor = lineColorList.find(v => v.name === sampleCode)
        if (!haveColor) {
          lineColorList.push({name: sampleCode, color: echartsColorList[lineColorList.length]})
        }
        const temColor = lineColorList[lineColorList.findIndex(v => v.name === sampleCode)].color
        const seriesTemplate = {
          name: yAxisOneLegendName,
          type:  'line',
          sampling: 'lttb',
          large: true,
          barGap: 0,
          symbol: templateContent.symbol ?? (targetObj.includes('rpt') ? 'rect' : 'none'),
          markPoint: {
            data: []
          },
          emphasis: {
            focus: "series"
          }
        }

        const seriesOriginalTemplate = {
          name: yAxisOneLegendName,
          index: i + 1,
          soc: yAxisOneLegendName,
          type: 'line',
          sampling: 'lttb',
          large: true,
          barGap: 0
        }
        const originalSeries = [{
            id: sampleCode,
            lineType: templateContent.lineType ?? 'solid',
            synchronization: templateContent.synchronization ??  (0 + seriesList.length),
            symbol: templateContent.symbol ?? (targetObj.includes('rpt') ? 'rect' : 'none'),
            maxPoint: templateContent.maxPoint ?? false,
            minPoint: templateContent.minPoint ?? false,
            connectNulls: Boolean(Number(templateContent.connectNulls))  ??  false,
            symbolSize: templateContent.symbolSize ?? 0,
            lineWidth: templateContent.lineWidth ?? 1.5,
            lineColor: templateContent.lineColor ?? temColor,
            itemColor: templateContent.itemColor ?? temColor,
            dataName:yAxisOneLegendName + '_' + firstTooltip.title,
            ...seriesOriginalTemplate
          }]

        // 判断是否有模板，并且模板是否有该值
        if(templateParam.legendData?.legendList === undefined || templateParam.legendData.legendList.includes(yAxisOneLegendName)){
          if(templateContent.symbolSize){
            seriesTemplate.symbolSize = templateContent.symbolSize
          }
          if(templateContent.connectNulls){
            seriesTemplate.connectNulls = Boolean(Number(templateContent.connectNulls))
          }
          if(templateContent.maxPoint){
            seriesTemplate.markPoint.data.push({type:'max',name:'max'})
          }
          if(templateContent.minPoint){
            seriesTemplate.markPoint.data.push({type:'min',name:'min'})
          }

          seriesList.push({
              ...seriesTemplate,
              id: sampleCode,
              // tooltip: {
              //   valueFormatter: (value) => {
              //     console.log('单轴')
              //     console.log(value)
              //     if(value === null) return null
              //     return firstTooltip.title + (value != null ? value + firstTooltip.unit : '');
              //   }
              // },
              lineStyle: {
                width: templateContent.lineWidth ?? 1.5,
                type: templateContent.lineType ?? 'solid',
                color: templateContent.lineColor ?? temColor
              },
              itemStyle: {
                color: templateContent.itemColor ?? temColor
              },
              data: yAxisOneList[i].data.map((item, index) => {
                return xAxisType === 'cycle' ? {id: index, value: [item[0], item[1]]} : {id: index, value: [item[2], item[1]]}
              }),
            })
        }

        // 双Y轴
        if (yAxisTwoList.length > 0 && this.hasDcirGroup) {
          for (let j = 0; j < this.dcirTiTleList.length; j++) {
            let findIndex = yAxisTwoList.findIndex(item => item.sampleCode == sampleCode && item.dcirTitleIndex == j)
            if (findIndex != -1) {
              // 由于两条线不同，故id不同，需替换
              templateContent = (templateParam.checkData && templateParam.checkData.length !== 0) ? (templateParam.checkData.filter(item => item.id === yAxisTwoList[findIndex].sampleCode + 'dcir' + yAxisTwoList[findIndex].dcirTitleIndex)[0] || {}) : {}
              if(templateParam.legendData?.legendList === undefined || templateParam.legendData.legendList.includes(yAxisOneLegendName)){
                seriesList.push(
                  {
                    ...seriesTemplate,
                    id: yAxisTwoList[findIndex].sampleCode + 'dcir' + yAxisTwoList[findIndex].dcirTitleIndex,
                    yAxisIndex: 1,
                    // tooltip: {
                    //   valueFormatter: (value) => {
                    //     console.log('双轴')
                    //     console.log(value)
                    //     return (yAxisTwoList[findIndex].dcirTitle || '') + '_DCIR增长率  ' + (value != null ? value + ' %' : '');
                    //   },
                    // },
                    lineStyle: {
                      width: templateContent.lineWidth ?? 1.5,
                      type: templateContent.lineType ?? 'dashed',
                      color: templateContent.lineColor ?? temColor
                    },
                    itemStyle: {
                      color: templateContent.itemColor ?? temColor
                    },
                    data: yAxisTwoList[findIndex].data.map((item, index) => {
                      return xAxisType === 'cycle' ? {id: index, value: [item[0], item[1]]} : {id: index, value: [item[2], item[1]]}
                    }),
                  }
                )
              }
              originalSeries.push(
                {
                  ...seriesOriginalTemplate,
                  id: yAxisTwoList[findIndex].sampleCode + 'dcir' + yAxisTwoList[findIndex].dcirTitleIndex,
                  lineType: templateContent.lineType ?? 'dashed',
                  synchronization: templateContent.synchronization ??  0 + seriesList.length,
                  symbol: templateContent.symbol ?? (targetObj.includes('rpt') ? 'rect' : 'none'),
                  maxPoint: templateContent.maxPoint ?? false,
                  minPoint: templateContent.minPoint ?? false,
                  connectNulls: Boolean(Number(templateContent.connectNulls))  ??  false,
                  symbolSize: templateContent.symbolSize ?? 0,
                  lineWidth: templateContent.lineWidth ?? 1.5,
                  lineColor: templateContent.lineColor ?? temColor,
                  itemColor: templateContent.itemColor ?? temColor,
                  dataName: yAxisOneLegendName + '_' + (yAxisTwoList[findIndex].dcirTitle || '') + '_DCIR增长率  ',
                }
              )
            }
          }
        }

         normalData.series.push(...originalSeries)
         normalLegent.push(yAxisOneLegendName)
      }
      // 判断原因：因修改图例名称时，会全部重新走一遍，如果不判断，原始值会被修改
      if(!this.originalData[targetObj]){
        this.originalData[targetObj] = originalParam ? _.cloneDeep (originalParam) : _.cloneDeep(normalData)
        this.originalData[targetObj].originalSeries = originalParam ? _.cloneDeep(originalParam.originalSeries) : _.cloneDeep(seriesList)
      }

      this.legendOptions[targetObj] = _.cloneDeep(normalLegent)
      this.editData[targetObj] = {
        ..._.cloneDeep(normalData),
        editSeries:_.cloneDeep(seriesList),
        originalSeries:originalParam ? _.cloneDeep(originalParam.originalSeries) : _.cloneDeep(seriesList),
        legend:templateParam.legendData?.legendList ?? _.cloneDeep(normalLegent),
        legendSort:templateParam.legendData?.legendSort ??  _.cloneDeep(normalLegent),
        legendRevealList:templateParam.legendData?.legendRevealList ??  _.cloneDeep(normalLegent).slice(0, 6),
        legendEditName:templateParam.legendData?.legendEditName ??  _.cloneDeep(normalLegent).map(v => {
          return {id: v,originName: v, previousName: '', newName: '', isReset: false}
        }),
        allData:templateParam.allData ?? {},
      }

      //修改this.editData的值，把之前修改过的值同步给this.editData
      for (let key of Object.keys(templateParam)) {
          if(['allData','checkData'].includes(key)) continue
          if(key === 'legendData'){
            // legendIndeterminate、checkAll、legendRevealcheckAll、legendRevealIndeterminate、legendRevealOptions
            for (let key1 of Object.keys(templateParam.legendData)) {
              if(templateParam.legendData?.[key1] !== undefined){
                this.editData[targetObj][key1] = templateParam.legendData[key1]
              }
            }
          }else{
            this.editData[targetObj][key] = templateParam[key]
          }
      }
      return seriesList
    },
     // 数据处理（处理图表值）
    _handleCycleEchartOptions(targetObj, xAxisType = 'cycle', seriesList) {
      // 模板数据
      const templateParam = this.reportChartTemplateList[targetObj].templateParamJson
      const originalParam = this.reportChartTemplateList[targetObj].originalParamJson

      const originalObj = this.originalData[targetObj]
      const firstItem = this.firstInit[targetObj] && !originalParam
      const editItem = this.editData[targetObj]


      const options = {
        textStyle: {
          fontFamily: "Times New Roman"
        },
        dataZoom: {
          type: 'inside'
        },
        backgroundColor: '#ffffff',
        animationDuration: 2000,
        title: {
          text: firstItem ? originalObj.chartTitle : editItem.chartTitle,
          left: 'center',
          top: firstItem ? originalObj.titleTop : editItem.titleTop,
          textStyle: {
            fontSize: 16,
            fontWeight: 500,
            color: "#000"
          }
        },
        grid: {
          show: true,
          top: firstItem ? originalObj.gridTop : editItem.gridTop,
          left: firstItem ? originalObj.gridLeft : editItem.gridLeft,
          right: firstItem ? originalObj.gridRight : editItem.gridRight,
          bottom: firstItem ? originalObj.gridBottom : editItem.gridBottom,
          borderWidth: 0.5,
          borderColor: "#ccc"
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          enterable: true,
          hideDelay: 300,
          position: function (point, params, dom, rect, size) {
            // 将tooltip显示在图表右侧
            return [ point[0] + 30, point[1] - size.contentSize[1] / 2 ];
          },
          extraCssText: 'max-height: 400px; overflow-y: auto; scrollbar-width: thin; scrollbar-color: #888 #f1f1f1; pointer-events: auto;',
          formatter: (params, ticket, callback) => {

            if (!params || params.length === 0) {
              return ''
            }

            // 构建 tooltip 内容
            let result = (params[0].axisValue || params[0].name || 'Unknown') + "<br>"

            // 过滤掉没有数据的系列
            const validParams = params.filter(item => {

              // 处理不同的数据格式
              let actualValue = null
              if (Array.isArray(item.value)) {
                actualValue = item.value[1] // 取数组的第二个元素
              } else {
                actualValue = item.value // 直接使用值
              }

              const isValid = actualValue !== null &&
                             actualValue !== undefined &&
                             actualValue !== '' &&
                             actualValue !== '-' &&
                             !isNaN(actualValue)

              return isValid
            })

            // 在axis trigger模式下，ECharts会按照距离鼠标最近的顺序传入参数
            // 第一个参数通常是最接近鼠标位置的数据点
            // 我们可以通过检查数据的距离来重新排序
            validParams.sort((a, b) => {
              // 如果有axisValue，说明这是axis trigger模式
              // 在这种模式下，我们可以通过数据点的位置来判断距离
              if (a.axisValue !== undefined && b.axisValue !== undefined) {
                // 计算每个数据点的X坐标值
                const aXValue = Array.isArray(a.value) ? a.value[0] : (a.axisValueLabel || a.axisValue)
                const bXValue = Array.isArray(b.value) ? b.value[0] : (b.axisValueLabel || b.axisValue)

                // 如果X坐标相同，按照Y值的绝对大小排序（数值越大越靠前）
                if (aXValue === bXValue) {
                  const aYValue = Array.isArray(a.value) ? Math.abs(a.value[1] || 0) : Math.abs(a.value || 0)
                  const bYValue = Array.isArray(b.value) ? Math.abs(b.value[1] || 0) : Math.abs(b.value || 0)
                  return bYValue - aYValue
                }
              }

              // 默认按照系列索引排序
              return (a.seriesIndex || 0) - (b.seriesIndex || 0)
            })

            validParams.forEach(item => {
              let displayText = ''
              let actualValue = Array.isArray(item.value) ? item.value[1] : item.value

              // 右边y轴 (非DCIR数据)
              if(!item.seriesId || item.seriesId.indexOf('dcir') === -1) {
                const title = targetObj.includes('Cap') ? '容量保持率' : '能量保持率'
                displayText = title + '  ' + actualValue + '%'
              } else {
                // DCIR数据
                try {
                  const sampleCode = item.seriesId.slice(0, item.seriesId.indexOf('dcir'))
                  const dcirTitleIndex = item.seriesId.slice(item.seriesId.indexOf('dcir') + 4, item.seriesId.length)
                  const yAxisTwoListName = targetObj.includes('rpt') ? 'rptDcirIncRateEchartList' : null

                  if (yAxisTwoListName && this.allDataJson && this.allDataJson[yAxisTwoListName]) {
                    let findIndex = this.allDataJson[yAxisTwoListName].findIndex(dataItem =>
                      dataItem.sampleCode == sampleCode && dataItem.dcirTitleIndex == dcirTitleIndex
                    )

                    if (findIndex !== -1) {
                      const dcirTitle = this.allDataJson[yAxisTwoListName][findIndex].dcirTitle || ''
                      displayText = dcirTitle + '_DCIR增长率  ' + actualValue + '%'
                    } else {
                      displayText = 'DCIR增长率  ' + actualValue + '%'
                    }
                  } else {
                    displayText = 'DCIR增长率  ' + actualValue + '%'
                  }
                } catch (error) {
                  displayText = 'DCIR增长率  ' + actualValue + '%'
                }
              }

              result += (item.marker || '●') + ' ' + (item.seriesName || 'Unknown') +
                       '<div style="width:20px;display: inline-block;"></div>' +
                       displayText + "<br>"
            })

            // 如果没有有效数据，返回基本信息而不是空字符串
            if (validParams.length === 0) {
              result += '暂无数据'
            }
            return result
          }
        },
        legend: {
          data: editItem.legendRevealList,
          backgroundColor: firstItem ? originalObj.legendBgColor : editItem.legendBgColor,
          itemWidth: firstItem ? originalObj.legendWidth : editItem.legendWidth,
          itemHeight: firstItem ? originalObj.legendHeight : editItem.legendHeight,
          itemGap: firstItem ? originalObj.legendGap : editItem.legendGap,
          orient: firstItem ? originalObj.legendOrient : editItem.legendOrient,
          right: firstItem ? originalObj.legendRight : editItem.legendRight,
          top: firstItem ? originalObj.legendTop : editItem.legendTop,
          textStyle: {
            fontSize: firstItem ? originalObj.legendFontSize : editItem.legendFontSize,
            color: "#000000"
          }
        },
        xAxis: [
          {
            name: firstItem ? originalObj.XTitle : editItem.XTitle,
            type: firstItem ? originalObj.xType : editItem.xType,
            nameLocation: 'middle', // 将名称放在轴线的中间位置
            nameGap: 30,
            nameTextStyle: {
              fontSize: 14, // 可以根据需要调整字体大小
              fontWeight: 500,
              color: "#000000" // 可以根据需要调整字体大小
            },
            axisTick: {show: false},
            axisLabel: {
              show: true,
              width: 0.5,
              fontSize: 14,
              color: "#000000",
              formatter: function (value) {
                return value
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              },
              onZero: false, // 次Y轴为数值轴且包含0刻度, 确保X轴的轴线不在次Y轴的0刻度上
            },
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                type: "solid",
                width: 0.5
              }
            },
            minInterval: 1,
            axisPointer: {
              label: {
                show: false,
                formatter: function(params) {
                  return params.value.toFixed(xAxisType === 'cycle' ? 0 : 2);
                }
              },
            }
          }
        ],
        yAxis: [
          {
            name: firstItem ? originalObj.YTitle : editItem.YTitle,
            type: firstItem ? originalObj.yType : editItem.yType,
            nameGap: firstItem ? originalObj.yTitleLetf : editItem.yTitleLetf,
            position: 'left',
            min: 70,
            max: 110,
            interval: 5,
            nameLocation: 'middle', // 将名称放在轴线的起始位置
            nameRotate: 90, // 旋转角度，使名称竖排
            nameTextStyle: {
              fontSize: 14, // 可以根据需要调整字体大小
              fontWeight: 500,
              color: "#000000"
            },
            splitLine: {
              show: true,  // 显示分隔线
              lineStyle: {
                type: 'solid',  // 设置分隔线的样式，比如虚线
                width: 0.5
              }
            },
            axisTick: {
              show: false,  // 显示刻度
            },
            axisLabel: {
              show: true,
              width: 0.5,
              fontSize: 14,
              color: "#000000"
              ,formatter: function (value) {
                return value
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
          }
        ],
        series: seriesList
      }

      if (targetObj.includes('rpt')) {
        options.yAxis.push(
          {
            name: firstItem ? originalObj.YTitle2 : editItem.YTitle2,
            type: firstItem ? originalObj.yType2 : editItem.yType2,
            position: 'right',
            min: -10,
            max: 150,
            interval: 20,
            nameGap: firstItem ? originalObj.yTitleRight : editItem.yTitleRight,
            nameLocation: 'middle', // 将名称放在轴线的起始位置
            nameRotate: 90, // 旋转角度，使名称竖排
            nameTextStyle: {
              fontSize: 14,
              fontWeight: 500,
              color: "#000000" // 可以根据需要调整字体大小
            },
            splitLine: {
              show: true,  // 显示分隔线
              lineStyle: {
                type: 'solid'  // 设置分隔线的样式，比如虚线
              }
            },
            axisTick: {
              show: true,  // 显示刻度
            },
            axisLabel: {
              show: true,
              width: 0.5,
              fontSize: 14,
              color: "#000000",
              formatter: function (value) {
                return value
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 0.5
              }
            },
            alignTicks: true,
          }
        )
      }

      // 非首次加载才需要或之前已经有模板了，并且模板有数据
      //处理图例
      //图例名称
      if (!this.firstInit[targetObj] || templateParam) {
        const newSeriesList = []
        _.cloneDeep(seriesList).forEach(v => {
          const haveNameList = editItem.legendEditName.filter(filterItem => filterItem.originName === v.name && filterItem.newName)
          v.name = haveNameList.length === 0 ? v.name : haveNameList[0].newName
          newSeriesList.push(v)
        })
        options.series = newSeriesList

        const legend = []

        editItem.legendSort.forEach(v => {
          if (editItem.legend.includes(v) && editItem.legendRevealList.includes(v)) {
            const haveList = editItem.legendEditName.filter(filterItem => filterItem.originName === v && filterItem.newName)
            legend.push(haveList.length === 0 ? v : haveList[0].newName)
          }
        })
        options.legend.data = legend
      }

      // 如果非首次编辑
      // 默认是水平，如果是水平，就不添加，垂直才添加
      if (editItem.legendOrient === 'vertical') {
        options.legend.orient = editItem.legendOrient
      }

      // X轴可能没有最大最小值、间隔
      if (!firstItem && editItem.xInterval && editItem.xType === 'value') {
        options.xAxis[0].min = editItem.xMin
        options.xAxis[0].max = editItem.xMax
        options.xAxis[0].interval = editItem.xInterval
      }
      if (!firstItem && editItem.yType === 'value') {
        options.yAxis[0].min = this.editData[targetObj].yMin
        options.yAxis[0].max = this.editData[targetObj].yMax
        options.yAxis[0].interval = this.editData[targetObj].yInterval
      }
      if (targetObj.includes('rpt') && !firstItem && editItem.yType2 === 'value') {
        options.yAxis[1].min = editItem.yMin2
        options.yAxis[1].max = editItem.yMax2
        options.yAxis[1].interval = editItem.yInterval2
      }

      return options
    },

    /*
    * 编辑图表
    */
    handleInitChart(targetObj) {
      this.initEchart(targetObj, this.xAxisType, `${targetObj}RetRateEchartList`, targetObj.includes('rpt') ? 'rptDcirIncRateEchartList' : null)
    },
    _handleInitLegendList(seriesList, legend) {
      if (this.echartObj[this.editObj]) this.echartObj[this.editObj].dispose();
      this.echartObj[this.editObj] = this.echarts.init(document.getElementById(this.editObj), 'walden', { devicePixelRatio: 2 })

      let chartOption = this._handleCycleEchartOptions(this.editObj, this.xAxisType, seriesList)

      chartOption.legend.data = legend
      this.echartObj[this.editObj].setOption(chartOption)
    },

    xAxisTypeChange(event) {
      this.xAxisType = event.target.value

      // 重新加载Echarts图
      this.isLoading = true
      setTimeout(() => {
        this.globalXMax = 0
        this.globalXInterval = 0
        this.editObjList.forEach(editObj => {
          this.firstInit[editObj] = true
          // 切换X轴类型需修改X轴标题
          this.originalData[editObj].XTitle = this.xAxisType === 'cycle' ? 'Cycle Number / N' : 'ETP / Wh'
          this.initEchart(editObj, this.xAxisType, `${editObj}RetRateEchartList`, editObj.includes('rpt') ? 'rptDcirIncRateEchartList' : null)
        })

        this.isLoading = false
      }, 100)
    },

    onCycCapPageChange(pageNo, pageSize) {
      this.cycCapLoading = true;
      this.cycCapPageNo = pageNo;
      this.cycCapPageSize = pageSize;
      getDongLiCycleReport({
        id: this.id,
        pageChange: "yes",
        cycCapPageNo: this.cycCapPageNo, cycCapPageSize: this.cycCapPageSize
      })
        .then(res => {
          this.data = res.data
          let json = jsonBigint({storeAsString: true})
          this.allDataJson = json.parse(res.data.allDataJson)

          this.cycCapTablePage = this.allDataJson.cycCapTablePage || {}
          this.cycCapTableList = this.cycCapTablePage.records
          this.cycCapPageNo = this.cycCapTablePage.current
          this.cycCapPageSize = this.cycCapTablePage.size
          this.cycCapTotal = this.cycCapTablePage.total
        }).finally(() => {
          this.cycCapLoading = false;
        })
    },
    onCycEngPageChange(pageNo, pageSize) {
      this.cycEngLoading = true;
      this.cycEngPageNo = pageNo;
      this.cycEngPageSize = pageSize;
      getDongLiCycleReport({
        id: this.id,
        pageChange: "yes",
        cycEngPageNo: this.cycEngPageNo, cycEngPageSize: this.cycEngPageSize
      })
        .then(res => {
          this.data = res.data
          let json = jsonBigint({storeAsString: true})
          this.allDataJson = json.parse(res.data.allDataJson)

          this.cycEngTablePage = this.allDataJson.cycEngTablePage || {}
          this.cycEngTableList = this.cycEngTablePage.records
          this.cycEngPageNo = this.cycEngTablePage.current
          this.cycEngPageSize = this.cycEngTablePage.size
          this.cycEngTotal = this.cycEngTablePage.total
        }).finally(() => {
          this.cycEngLoading = false;
        })
    },

    reExport(rebuildFlag) {
      // this.$store.commit('setTaskFilterData', this.queryParam);
      // this.$router.push('/cycleReportBuild');
      this.$store.commit('setTaskFilterData', this.data);
      if (rebuildFlag) {
        this.$router.push('/dong_li_report_build');
      } else {
        this.$router.push('/dong_li_report_build?id=' + this.data.id);
      }
    },
    exportCycleReport() {
      this.isLoading = true
      exportDongLiCycleReport({
        id: this.data.id
      }).then(res => {
        if (res.data.size > 0) {
          const reportName = this.data.reportName
          const fileName = '循环数据_' + reportName + '.xlsx'
          downloadfile1(res, fileName)
        } else {
          this.$message.warning("暂无数据！")
        }
      }).catch(error => {
        this.$message.warning("导出失败！")
      }).finally(() => {
        this.isLoading = false
      })
    },
  }
}
</script>
<style lang="less" scoped>
@import "./css/calendar.less";

/* 固定列 */
/deep/ .right-content .ant-table-thead tr:nth-child(1) th:nth-child(1),
/deep/ .right-content .ant-table-tbody tr td:nth-child(1) {
  position: sticky;
  left: 0;
  z-index: 11;
}
/* 固定列数据背景颜色 */
/deep/ .right-content .ant-table-tbody tr td:nth-child(1) {
  background-color: #FFFFFF;
}

.right-top {
  position: absolute;
  top: 10px;
  right: 10px;
  height: 30px;
  display: flex;
  align-items: end;
}

/deep/ .ant-btn-div .ant-btn > i,
/deep/ .ant-btn-div .ant-btn > span {
  display: inline-block;
}

.table-title-div {
  height: 32px;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}
</style>